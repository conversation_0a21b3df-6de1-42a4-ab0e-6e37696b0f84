<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="1" author="robert">
        <preConditions onFail="MARK_RAN">
            <not><tableExists tableName="pet" /></not>
        </preConditions>
        <createTable tableName="pet">
            <column name="version" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="id" type="UUID">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
            <column name="born" type="DATE">
                <constraints nullable="false"/>
            </column>
            <column name="owner_id" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="species" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint
                constraintName="uk_pet"
                tableName="pet"
                columnNames="name,owner_id"
        />
        <addForeignKeyConstraint
                constraintName="fk_pet_owner_id"
                baseTableName="pet"
                baseColumnNames="owner_id"
                referencedTableName="owner"
                referencedColumnNames="id"
        />
    </changeSet>
</databaseChangeLog>
