<form [formGroup]="form" (ngSubmit)="onSubmitClicked()">
  <div class="flex flex-col gap-2 pt-4">
    <fieldset class="fieldset w-full">
      <legend class="fieldset-legend">Name</legend>
      <input
        aria-label="Name"
        type="text"
        class="input input-bordered w-full"
        placeholder="Enter a name"
        formControlName="name"
      />
    </fieldset>
    <fieldset class="fieldset w-full">
      <legend class="fieldset-legend">Address</legend>
      <input
        aria-label="Address"
        type="text"
        class="input input-bordered w-full"
        placeholder="Enter an address"
        formControlName="address"
      />
    </fieldset>
    <fieldset class="fieldset w-full">
      <legend class="fieldset-legend">Contact</legend>
      <input
        aria-label="Contact"
        type="text"
        class="input input-bordered w-full"
        placeholder="Enter a contact"
        formControlName="contact"
      />
    </fieldset>
  </div>
  <div class="join py-4">
    <button type="submit" class="btn join-item" [disabled]="!isSubmittable">
      Ok
    </button>
    <button type="button" class="btn join-item" (click)="onCancelClicked()">
      Cancel
    </button>
  </div>
</form>
