<script lang="ts">
  let {
    checked = $bindable(false),
    clicked = $bindable(0),
    disabled = false,
    name,
    outlined = false,
    title = undefined,
    onclick = undefined,
    ...elementProps
  } = $props();

  let element;
  export function focus() {
    element?.focus();
  }

  function handleClick(_event: MouseEvent) {
    checked = !checked;
    clicked++;
    onclick?.(_event);
  }
</script>

<button
  type="button"
  aria-label={name}
  bind:this={element}
  {...elementProps}
  {title}
  {disabled}
  class:disabled
  class="text-xl font-bold border-2 border-solid border-indigo-500 w-12 h-12 rounded-full p-2 disabled:opacity-50 hover:opacity-90 focus:ring bg-indigo-500"
  class:outlined
  class:text-indigo-500={outlined}
  class:bg-transparent={outlined}
  class:text-white={!outlined}
  class:bg-indigo-500={!outlined}
  onclick={handleClick}
>
  <div class="flex justify-center items-center">
    <i
      {title}
      aria-hidden="true"
      class="material-icons icon text-xl select-none"
    >
      {name}
    </i>
  </div>
</button>
