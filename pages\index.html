<!DOCTYPE html>
<html>
  <body>
    <h1>Documentation with Github Pages</h1>
    <p>
      With
      <a href="https://docs.github.com/en/github/working-with-github-pages/about-github-pages">GitHub Pages,</a>
      HTML, CSS and JavaScript files are published directly from the repository.
    </p>
    <p>
      <a href="html/server/rest/EnumRestApi.html" target="_blank"><code>EnumRestApi</code></a>
    </p>
    <p>
      <a href="html/server/rest/OwnerRestApi.html" target="_blank"><code>OwnerRestApi</code></a>
    </p>
    <p>
      <a href="html/server/rest/PetRestApi.html" target="_blank"><code>PetRestApi</code></a>
    </p>
    <p>
      <a href="html/server/rest/VetRestApi.html" target="_blank"><code>VetRestApi</code></a>
    </p>
    <p>
      <a href="html/server/rest/VisitRestApi.html" target="_blank"><code>VisitRestApi</code></a>
    </p>
    <h1>Integration testing with Playwright</h1>
    <p>
      <a href="https://playwright.dev/" target="_blank">Playwright</a>
      enables reliable end-to-end testing for web apps.
    </p>
    <p>
      <a href="html/client-angular/playwright/index.html" target="_blank"><code>client-angular</code></a>
    </p>
    <p>
      <a href="html/client-svelte/playwright/index.html" target="_blank"><code>client-svelte</code></a>
    </p>
    <h1>Component testing with JUnit5</h1>
    <p>
      <a href="https://junit.org/junit5/" target="_blank">JUnit5</a>
      is used to develop test-driven code and simultaneously check the quality and reliability of the code in the Java projects.
    </p>
    <p>
      <a href="html/backend-api/junit5/index.html" target="_blank"><code>backend-api</code></a>
    </p>
    <p>
      <a href="html/backend-data/junit5/index.html" target="_blank"><code>backend-data</code></a>
    </p>
    <p>
      <a href="html/server/junit5/index.html" target="_blank"><code>server</code></a>
    </p>
    <h1>Coverage analysis with JaCoCo</h1>
    <p>
      <a href="https://www.eclemma.org/jacoco/" target="_blank">JaCoCo</a>
      is used to measure the quality of the code in Java projects in terms of test coverage.
    </p>
    <p>
      <a href="html/backend-api/jacoco/index.html" target="_blank"><code>backend-api</code></a>
    </p>
    <p>
      <a href="html/backend-data/jacoco/index.html" target="_blank"><code>backend-data</code></a>
    </p>
  </body>
</html>
