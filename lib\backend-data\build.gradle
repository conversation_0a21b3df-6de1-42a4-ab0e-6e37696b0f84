plugins {
    id 'java-library'
    id 'eclipse'
    id 'jacoco'
    id 'io.spring.dependency-management'
    id 'org.springframework.boot'
}

dependencies {
    api project(':lib:backend-api')
    // https://projectlombok.org
    implementation('org.projectlombok:lombok')
    annotationProcessor('org.projectlombok:lombok')
    // https://spring.io/projects/spring-boot
    api('org.springframework.boot:spring-boot-starter')
    api('org.springframework.boot:spring-boot-starter-security')
    api('org.springframework.boot:spring-boot-starter-web')
    api('org.springframework.boot:spring-boot-starter-validation')
    // https://spring.io/projects/spring-graphql
    api('org.springframework.boot:spring-boot-starter-graphql')
    api('org.springframework.boot:spring-boot-starter-actuator')
    api('io.micrometer:micrometer-registry-prometheus')
    // https://spring.io/projects/spring-data
    api('org.springframework.boot:spring-boot-starter-data-rest')
    api('org.springframework.boot:spring-boot-starter-data-jpa')
    api('org.springframework.data:spring-data-rest-hal-explorer')
    // https://liquibase.org/
    api('org.liquibase:liquibase-core')
    // http://hsqldb.org/
    runtimeOnly('org.hsqldb:hsqldb')
}
dependencies {
    // https://spring.io/projects/spring-boot
    testImplementation('org.springframework.boot:spring-boot-starter-test')
    // https://spring.io/projects/spring-graphql
    testImplementation('org.springframework.graphql:spring-graphql-test')
    // https://spring.io/projects/spring-restdocs
    testImplementation('org.springframework.restdocs:spring-restdocs-mockmvc')
    // https://junit.org/junit5
    testImplementation('org.junit.jupiter:junit-jupiter')
    testRuntimeOnly('org.junit.platform:junit-platform-launcher')
    // https://site.mockito.org/
    testImplementation('org.mockito:mockito-junit-jupiter')
}

test {
    filter {
        failOnNoMatchingTests false
    }
    reports {
        html.required = true
        html.destination = file("${rootDir}/pages/html/" + project.name + "/junit5")
    }
    jacoco {
        enabled = true
    }
}

jacocoTestReport {
    mustRunAfter test
    reports {
        csv.required = false
        html.required = true
        html.destination file("${rootDir}/pages/html/" + project.name + "/jacoco")
        xml.required = true
    }
}

jar {
    enabled = true
    manifest {
        attributes 'Specification-Title': project.name
        attributes 'Specification-Version': VERSION
        attributes 'Implementation-Title': project.name
        attributes 'Implementation-Version': project.version
    }
}

bootJar {
    enabled = false
}

bootRun {
    enabled = false
}

bootBuildImage {
    enabled = false
}
