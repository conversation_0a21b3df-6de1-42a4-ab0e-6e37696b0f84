services:
  client-angular:
    hostname: client-angular
    image: ${COMPOSE_PROJECT_NAME}/client-angular
    ports:
      - 5052:5052
  client-svelte:
    hostname: client-svelte
    image: ${COMPOSE_PROJECT_NAME}/client-svelte
    ports:
      - 5050:5050
  server:
    hostname: server
    image: ${COMPOSE_PROJECT_NAME}/server
    ports:
      - 5005:5005
      - 8080:8080
    environment:
      - _JAVA_OPTIONS=-Xdebug -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
      - SERVER_PORT=8080
      - SPRING_DATASOURCE_URL=************************************
      - SPRING_DATASOURCE_USERNAME=SA
      - SPRING_DATASOURCE_PASSWORD=
      - SPRING_PROFILES_ACTIVE=dev
