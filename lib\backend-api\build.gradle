plugins {
    id 'java-library'
    id 'eclipse'
    id 'jacoco'
    id 'io.spring.dependency-management'
    id 'org.springframework.boot'
}

dependencies {
    // https://projectlombok.org
    implementation('org.projectlombok:lombok')
    annotationProcessor('org.projectlombok:lombok')
    // https://spring.io/projects/spring-framework
    api('org.springframework:spring-core')
    // https://spring.io/projects/spring-hateoas
    api('org.springframework.hateoas:spring-hateoas')
    // http://querydsl.com/
    api("com.querydsl:querydsl-core")
    api("com.querydsl:querydsl-jpa::jakarta")
    annotationProcessor("com.querydsl:querydsl-apt::jakarta")
    // https://github.com/graphql-java/graphql-java
    implementation('com.graphql-java:graphql-java')
    // https://projects.eclipse.org/projects/ee4j.jpa
    api('jakarta.persistence:jakarta.persistence-api')
    annotationProcessor("jakarta.persistence:jakarta.persistence-api")
    // https://projects.eclipse.org/projects/ee4j.jta
    api('jakarta.transaction:jakarta.transaction-api')
    // https://hibernate.org/validator
    api('org.hibernate.validator:hibernate-validator')
    // https://github.com/FasterXML/jackson-docs
    api('com.fasterxml.jackson.core:jackson-databind')
    api('com.fasterxml.jackson.datatype:jackson-datatype-jdk8')
    api('com.fasterxml.jackson.datatype:jackson-datatype-jsr310')
}
dependencies {
    // https://junit.org/junit5
    testImplementation('org.junit.jupiter:junit-jupiter')
    testRuntimeOnly('org.junit.platform:junit-platform-launcher')
    // https://site.mockito.org/
    testImplementation('org.mockito:mockito-junit-jupiter')
}

test {
    filter {
        failOnNoMatchingTests false
    }
    reports {
        html.required = true
        html.destination = file("${rootDir}/pages/html/" + project.name + "/junit5")
    }
    jacoco {
        enabled = true
    }
}

jacocoTestReport {
    mustRunAfter test
    reports {
        csv.required = false
        html.required = true
        html.destination file("${rootDir}/pages/html/" + project.name + "/jacoco")
        xml.required = true
    }
}

jar {
    enabled = true
    manifest {
        attributes 'Specification-Title': project.name
        attributes 'Specification-Version': VERSION
        attributes 'Implementation-Title': project.name
        attributes 'Implementation-Version': project.version
    }
}

bootJar {
    enabled = false
}

bootRun {
    enabled = false
}

bootBuildImage {
    enabled = false
}

processResources {
    with copySpec {
        from("${rootDir}")
        include 'VERSION'
        include 'VERSION.md'
    }
}
