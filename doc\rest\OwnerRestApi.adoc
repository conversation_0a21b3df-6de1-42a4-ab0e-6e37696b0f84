:project-dir: ../..
:restdoc-dir: {project-dir}/lib/backend-data/build/generated-snippets
= Owner-API

REST-API for managing pet owners acting as clients in the pet clinic application.
It provides standard CRUD operations and a search endpoint that returns items.

== Model

The main entity of an _Owner_ managed by this controller for persistence.
The entity extends `JsonJpaEntity` which provides unique identifier and version for optimistic locking.
Each owner represents a pet clinic client who owns one or more pets.
The owner requires a valid name and address.

.Entity
[source,java,options="nowrap"]
----
include::{project-dir}/lib/backend-api/src/main/java/esy/api/client/Owner.java[indent=0,tags=properties]
----

A simplified representation of an _Owner_ for item selection purposes.

.Item
[source,java,options="nowrap"]
----
include::{project-dir}/lib/backend-api/src/main/java/esy/api/client/OwnerItem.java[indent=0,tags=properties]
----

== Operations

=== `POST /api/owner`

This operation creates a new _Owner_ entity.

****

.CURL
include::{restdoc-dir}/post-api-owner/curl-request.adoc[]

.Request
include::{restdoc-dir}/post-api-owner/http-request.adoc[]

.Response
include::{restdoc-dir}/post-api-owner/response-body.adoc[]

****

This operation reports `Created` or code 201 if the object was successfully created.

This operation reports `Bad Request` or code 400 if the data was not processed.

This operation reports `Conflict` or code 409 if the data already exists, is incomplete, or is invalid.

=== `PUT /api/owner/{id}`

This operation updates an existing _Owner_ entity or creates a new one.

****

.CURL
include::{restdoc-dir}/put-api-owner/curl-request.adoc[]

.Request
include::{restdoc-dir}/put-api-owner/http-request.adoc[]

.Response
include::{restdoc-dir}/put-api-owner/response-body.adoc[]

****
This operation reports `Ok` or code 200 if the object was successfully updated.

This operation reports `Created` or code 201 if the object was successfully created.

This operation reports `Bad Request` or code 400 if the data was not processed.

This operation reports `Conflict` or code 409 if the data is incomplete or is invalid.

=== `PATCH /api/owner/{id}`

This operation partially updates an existing _Owner_ entity.
The following example shows the update of the name.

****

.CURL
include::{restdoc-dir}/patch-api-owner-name/curl-request.adoc[]

.Request
include::{restdoc-dir}/patch-api-owner-name/http-request.adoc[]

.Response
include::{restdoc-dir}/patch-api-owner-name/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the object was successfully updated.

This operation reports `Bad Request` or code 400 if the data was not processed.

This operation reports `Not found` or code 404 if the entity does not exist.

This operation reports `Conflict` or code 409 if the data is incomplete or is invalid.

=== `GET /api/owner`

This operation returns all persisted _Owner_ entities.

It supports advanced query parameters for filtering and sorting, e.g.

`?name=Max`::
Find owners with name containing "Max" (case-insensitive)
`?name=Max%`::
Find owners with name starting with "Max" (case-insensitive)
`?address=Street`::
Find owners with address containing "Street" (case-insensitive)
`?contact=@gmx.at`::
Find owners with contact information containing "@gmx.at" (case-insensitive)

It supports sorting and pagination, e.g.

`?sort=name,asc`::
Sort owners by name in ascending order
`?size=10&page=1`::
Find 10 owners on page 1

****

.CURL
include::{restdoc-dir}/get-api-owner/curl-request.adoc[]

.Request
include::{restdoc-dir}/get-api-owner/http-request.adoc[]

.Response
include::{restdoc-dir}/get-api-owner/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the data was successfully loaded.

=== `GET /api/owner/search/findAllItem`

This operation returns all persisted _Owner_ entities as simplified _OwnerItem_ objects for selection purposes.
The items are sorted by name in ascending order.

****

.CURL
include::{restdoc-dir}/get-api-owner-item/curl-request.adoc[]

.Request
include::{restdoc-dir}/get-api-owner-item/http-request.adoc[]

.Response
include::{restdoc-dir}/get-api-owner-item/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the data was successfully loaded.

=== `GET /api/owner/{id}`

This operation returns a single persisted _Owner_ entity.

****

.CURL
include::{restdoc-dir}/get-api-owner-by-id/curl-request.adoc[]

.Request
include::{restdoc-dir}/get-api-owner-by-id/http-request.adoc[]

.Response
include::{restdoc-dir}/get-api-owner-by-id/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the data was successfully loaded.

This operation reports `Not found` or code 404 if the data does not exist.

=== `DELETE /api/owner/{id}`

This operation deletes a single persisted _Owner_ entity.

****

.CURL
include::{restdoc-dir}/delete-api-owner/curl-request.adoc[]

.Request
include::{restdoc-dir}/delete-api-owner/http-request.adoc[]

.Response
include::{restdoc-dir}/delete-api-owner/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the data was successfully deleted.

This operation reports `Not found` or code 404 if the data does not exist.
