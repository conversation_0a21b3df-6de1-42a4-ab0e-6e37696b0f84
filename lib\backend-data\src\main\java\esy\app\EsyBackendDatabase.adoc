= Spezifikation für die Datenbank

Der Dienst nutzt eine zentrale Konfigurationsdatei für die Datenbank.
Sie wird nur beim Start der Anwendung gelesen.

.database.properties
[source,text]
----
include::../../../resources/database.properties[]
----

Der Dienst nutzt XML-Skripte für die Erstellung und Migration der Datenbank.
DDL-Statements werden in einem speziellen XML-Format geskriptet.
Für jedes Datenbankobjekt gibt es ein eigenes Skript.
Da die Reihenfolge der Ausführung der Skripte relevant ist, werden die Skripte für die Datenbankobjekte zu einem Changelog-Skript zusammengefasst.
Das Changelog-Skript wird beim Start der Anwendung ausgeführt.
Abhängig vom aktuellen Zustand der Datenbank werden Skripte ausgeführt und erzeugen neue Datenbankobjekte.
Bereits auf dieser Datenbank ausgeführte Skripte werden ignoriert.

.liquibase/changelog.xml
[source,text]
----
include::../../../resources/liquibase/changelog.xml[]
----
