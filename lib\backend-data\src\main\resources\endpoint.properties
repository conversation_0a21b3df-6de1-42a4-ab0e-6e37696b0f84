# org.springframework.boot.autoconfigure.web.ServerProperties
server.port=8080
server.compression.enabled=false
# org.springframework.boot.autoconfigure.graphql.GraphQlProperties
spring.graphql.http.path=/api/graphql
spring.graphql.graphiql.path=/api/graphiql
spring.graphql.graphiql.enabled=true
# org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties
spring.data.web.pageable.default-page-size=9999
spring.data.web.pageable.max-page-size=9999
# org.springframework.boot.autoconfigure.data.rest.RepositoryRestProperties
spring.data.rest.default-page-size=9999
spring.data.rest.max-page-size=9999
# org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties
management.endpoints.web.exposure.include=health
# org.springframework.boot.actuate.autoconfigure.health.HealthEndpointProperties
management.endpoint.health.probes.enabled=true
# Verzögerung nach dem Start (2m)
schedule.startupDelayMs=120000
