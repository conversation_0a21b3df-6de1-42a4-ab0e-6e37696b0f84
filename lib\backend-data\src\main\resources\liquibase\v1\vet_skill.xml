<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="1" author="robert">
        <preConditions onFail="MARK_RAN">
            <not><tableExists tableName="vet_skill" /></not>
        </preConditions>
        <createTable tableName="vet_skill">
            <column name="id" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="skill" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addForeignKeyConstraint
                constraintName="fk_vet_skill_id"
                baseTableName="vet_skill"
                baseColumnNames="id"
                referencedTableName="vet"
                referencedColumnNames="id"
        />
    </changeSet>
</databaseChangeLog>
