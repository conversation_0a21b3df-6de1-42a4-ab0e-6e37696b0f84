<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="1" author="robert">
        <preConditions onFail="MARK_RAN">
            <not><tableExists tableName="vet" /></not>
        </preConditions>
        <createTable tableName="vet">
            <column name="version" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="id" type="UUID">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint
                constraintName="uk_vet"
                tableName="vet"
                columnNames="name"
        />
    </changeSet>
</databaseChangeLog>
