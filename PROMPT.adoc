= AI-driven development and maintenance

.Sanity Check of REST-API documentation
----
Check all adoc files in @doc/rest. Use @doc/template/SpringRestApi.adoc as template for the overall structure of an adoc file. Use notes from the template for the sanity check. Fix typos. Compare the description in the adoc files with the implementation in @lib/backend-api and @lib/backend-data. Find potential errors but only add comments in the adoc file with instructions on how to fix the issue.
----
