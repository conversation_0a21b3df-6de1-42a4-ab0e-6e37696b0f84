# org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties
spring.liquibase.enabled=true
spring.liquibase.change-log=classpath:liquibase/changelog.xml
# org.springframework.boot.autoconfigure.jdbc.DatasourceProperties
spring.datasource.url=************************************
spring.datasource.username=SA
spring.datasource.password=
# org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties
spring.jpa.properties.hibernate.show_sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.generate_statistics=true
spring.jpa.properties.hibernate.jdbc.batch_size=5
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.batch_versioned_data=true
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true
# org.springframework.boot.autoconfigure.orm.jpa.JpaProperties
spring.jpa.open-in-view=false
