@import "tailwindcss";
@plugin "daisyui";

@font-face {
  font-family: "Material Icons";
  src: url("/MaterialIcons-Regular.ttf") format("truetype");
}

.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px; /* Preferred icon size */
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  /* Support for all WebKit browsers. */
  -webkit-font-smoothing: antialiased;
  /* Support for Safari and Chrome. */
  text-rendering: optimizeLegibility;
  /* Support for Firefox. */
  -moz-osx-font-smoothing: grayscale;
}

h1,
.h1 {
  @apply text-3xl font-normal pt-4 pb-2 mx-2;
  letter-spacing: -1.5px;
}

h2,
.h2 {
  @apply text-2xl font-normal pt-4 pb-2 mx-2;
  letter-spacing: -0.5px;
}

h3,
.h3 {
  @apply text-xl font-normal pt-4 pb-2 mx-2;
  letter-spacing: 0px;
}

h4,
.h4 {
  @apply text-lg font-normal pt-2 pb-1 mx-2;
  letter-spacing: 0.25px;
}

h5,
.h5 {
  @apply text-base font-medium pt-2 pb-1 mx-2;
  letter-spacing: 0.15px;
}

h6,
.h6 {
  @apply text-base font-medium mx-2;
  letter-spacing: 0px;
}

hr {
  @apply border-t border-gray-400 border-solid;
}

a {
  @apply underline text-blue-600;
}
