plugins {
    id 'base'
}

tasks.register('composeUp') {
    mustRunAfter ':app:client-angular:buildImage'
    mustRunAfter ':app:client-svelte:buildImage'
    mustRunAfter ':app:server:buildImage'
    doLast {
        println '\nDeploying with latest'
        exec {
            workingDir "${projectDir}"
            executable 'docker'
            args 'compose', '-p', 'petclinic', '-f', 'compose.yml', 'up', '--detach', '--no-build'
        }
    }
}

tasks.register('composeStop') {
    doLast {
        exec {            
            workingDir "${projectDir}"
            executable 'docker'
            args 'compose', '-p', 'petclinic', '-f', 'compose.yml', 'stop'
        }
    }
}

tasks.register('composeDown') {
    doLast {
        exec {
            workingDir "${projectDir}"
            executable 'docker'
            args 'compose', '-p', 'petclinic', '-f', 'compose.yml', 'down'
        }
    }
}

tasks.register('versionCheck', VersionCheckTask.class)  {
    group 'verification'
    allFileWithVersion.from("Chart.yaml")
}

tasks.register('lint') {
    group = 'verification'
}

tasks.register('format') {
    group = 'build'
}
