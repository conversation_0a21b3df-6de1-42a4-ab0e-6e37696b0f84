:project-dir: ../..
:restdoc-dir: {project-dir}/lib/backend-data/build/generated-snippets
= Enum-API

REST-API for managing configurable enumerations in the pet clinic application.
It provides operations to create, update, and retrieve enumeration values organized by discriminator.
Each enumeration type is identified by a discriminator and contains ordered items with unique numeric codes, names, and display texts.

== Model

The main entity of an _Enum_ managed by this controller for persistence.
The entity extends `JsonJpaEntity` which provides unique identifier and version for optimistic locking.
Each enum represents an extensible enumeration item.
Enums are organized by discriminator for logical grouping (e.g., "SPECIES", "SKILL").
Within each discriminator group, codes and names must be unique.
Codes are numeric.

.Entity
[source,java,options="nowrap"]
----
include::{project-dir}/lib/backend-api/src/main/java/esy/api/info/Enum.java[indent=0,tags=properties]
----

A simplified representation of an _Enum_ for item selection purposes.

.EnumItem
[source,java,options="nowrap"]
----
include::{project-dir}/lib/backend-api/src/main/java/esy/api/info/EnumItem.java[indent=0,tags=properties]
----

== Operations

=== `POST /api/enum/{art}`

This operation creates a new _Enum_ entity for a specific discriminator.

****

.CURL
include::{restdoc-dir}/post-api-enum/curl-request.adoc[]

.Request
include::{restdoc-dir}/post-api-enum/http-request.adoc[]

.Response
include::{restdoc-dir}/post-api-enum/response-body.adoc[]

****

This operation reports `Created` or code 201 if the object was successfully created.

This operation reports `Bad Request` or code 400 if the data was not processed.

This operation reports `Conflict` or code 409 if the object already exists.

=== `PUT /api/enum/{art}/{code}`

This operation updates an existing _Enum_ entity for a specific discriminator and code.

****

.CURL
include::{restdoc-dir}/put-api-enum/curl-request.adoc[]

.Request
include::{restdoc-dir}/put-api-enum/http-request.adoc[]

.Response
include::{restdoc-dir}/put-api-enum/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the object was successfully updated.

This operation reports `Not found` or code 404 if the enum does not exist.

This operation reports `Bad Request` or code 400 if the data was not processed.

=== `GET /api/enum/{art}`

This operation returns all persisted _Enum_ entities for a specific discriminator.
Results are ordered by discriminator and code.

****

.CURL
include::{restdoc-dir}/get-api-enum/curl-request.adoc[]

.Request
include::{restdoc-dir}/get-api-enum/http-request.adoc[]

.Response
include::{restdoc-dir}/get-api-enum/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the data was successfully loaded.
The result can be empty if no enums exist for the discriminator.

=== `DELETE /api/enum/{art}/{code}`

This operation deletes a single persisted _Enum_ entity for a specific discriminator and code.

****

.CURL
include::{restdoc-dir}/delete-api-enum/curl-request.adoc[]

.Request
include::{restdoc-dir}/delete-api-enum/http-request.adoc[]

.Response
include::{restdoc-dir}/delete-api-enum/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the data was successfully deleted.

This operation reports `Not found` or code 404 if the enum does not exist.
