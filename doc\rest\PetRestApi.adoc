:project-dir: ../..
:restdoc-dir: {project-dir}/lib/backend-data/build/generated-snippets
= Pet-API

REST-API for managing pets in the pet clinic application.
It provides standard CRUD operations and a search endpoint that returns items.

== Model

The main entity of a _Pet_ managed by this controller for persistence.
The entity extends `JsonJpaEntity` which provides unique identifier and version for optimistic locking.
Each pet represents an animal owned by a client and can have multiple veterinary visits.
The pet requires a valid name and species.
Each pet must have an owner.

.Entity
[source,java,options="nowrap"]
----
include::{project-dir}/lib/backend-api/src/main/java/esy/api/client/Pet.java[indent=0,tags=properties]
----

A simplified representation of a _Pet_ for item selection purposes.

.Item
[source,java,options="nowrap"]
----
include::{project-dir}/lib/backend-api/src/main/java/esy/api/client/PetItem.java[indent=0,tags=properties]
----

== Operations

=== `POST /api/pet`

This operation creates a new _Pet_ entity.

****

.CURL
include::{restdoc-dir}/post-api-pet/curl-request.adoc[]

.Request
include::{restdoc-dir}/post-api-pet/http-request.adoc[]

.Response
include::{restdoc-dir}/post-api-pet/response-body.adoc[]

****

This operation reports `Created` or code 201 if the object was successfully created.

This operation reports `Bad Request` or code 400 if the data was not processed.

This operation reports `Conflict` or code 409 if the data already exists, is incomplete, or is invalid.

=== `PUT /api/pet/{id}`

This operation updates an existing _Pet_ entity or creates a new one.

****

.CURL
include::{restdoc-dir}/put-api-pet/curl-request.adoc[]

.Request
include::{restdoc-dir}/put-api-pet/http-request.adoc[]

.Response
include::{restdoc-dir}/put-api-pet/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the object was successfully updated.

This operation reports `Created` or code 201 if the object was successfully created.

This operation reports `Bad Request` or code 400 if the data was not processed.

This operation reports `Conflict` or code 409 if the data is incomplete or is invalid.

=== `PATCH /api/pet/{id}`

This operation partially updates an existing _Pet_ entity.
The following example shows the update of the name.

****

.CURL
include::{restdoc-dir}/patch-api-pet-name/curl-request.adoc[]

.Request
include::{restdoc-dir}/patch-api-pet-name/http-request.adoc[]

.Response
include::{restdoc-dir}/patch-api-pet-name/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the object was successfully updated.

This operation reports `Bad Request` or code 400 if the data was not processed.

This operation reports `Not found` or code 404 if the entity does not exist.

This operation reports `Conflict` or code 409 if the data is incomplete or is invalid.

=== `GET /api/pet`

This operation returns all persisted _Pet_ entities.

It supports advanced query parameters for filtering and sorting, e.g.

`?name=Max`::
Find pets with name containing "Max" (case-insensitive)
`?name=Max%`::
Find pets with name starting with "Max" (case-insensitive)
`?species=dog`::
Find pets with species containing "dog" (case-insensitive)
`?owner.name=Smith`::
Find pets whose owner's name contains "Smith" (case-insensitive)

It supports sorting and pagination, e.g.

`?sort=name,asc`::
Sort pets by name in ascending order
`?sort=born,desc`::
Sort pets by birth date in descending order
`?size=10&page=1`::
Find 10 pets on page 1

****

.CURL
include::{restdoc-dir}/get-api-pet/curl-request.adoc[]

.Request
include::{restdoc-dir}/get-api-pet/http-request.adoc[]

.Response
include::{restdoc-dir}/get-api-pet/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the data was successfully loaded.

=== `GET /api/pet/search/findAllItem`

This operation returns all persisted _Pet_ entities as simplified _PetItem_ objects for selection purposes.
The items are sorted by name in ascending order.

****

.CURL
include::{restdoc-dir}/get-api-pet-item/curl-request.adoc[]

.Request
include::{restdoc-dir}/get-api-pet-item/http-request.adoc[]

.Response
include::{restdoc-dir}/get-api-pet-item/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the data was successfully loaded.

=== `GET /api/pet/{id}`

This operation returns a single persisted _Pet_ entity.

****

.CURL
include::{restdoc-dir}/get-api-pet-by-id/curl-request.adoc[]

.Request
include::{restdoc-dir}/get-api-pet-by-id/http-request.adoc[]

.Response
include::{restdoc-dir}/get-api-pet-by-id/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the data was successfully loaded.

This operation reports `Not found` or code 404 if the data does not exist.

=== `DELETE /api/pet/{id}`

This operation deletes a single persisted _Pet_ entity.

****

.CURL
include::{restdoc-dir}/delete-api-pet/curl-request.adoc[]

.Request
include::{restdoc-dir}/delete-api-pet/http-request.adoc[]

.Response
include::{restdoc-dir}/delete-api-pet/response-body.adoc[]

****

This operation reports `Ok` or code 200 if the data was successfully deleted.

This operation reports `Not found` or code 404 if the data does not exist.
