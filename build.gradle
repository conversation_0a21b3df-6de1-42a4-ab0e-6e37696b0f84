/*
 * https://guides.gradle.org/creating-new-gradle-builds/
 */

plugins {
    id 'base'
    id 'eclipse'
    id 'idea'
    id 'com.palantir.git-version'
    id 'org.asciidoctor.jvm.convert'
}

eclipse {
    classpath {
        downloadJavadoc = true
        downloadSources = true
    }
}

idea {
    module {
        downloadJavadoc = true
        downloadSources = true
    }
}

allprojects {
    new File("${rootDir}/README.adoc").withReader('utf-8') { description = it.readLine().minus('= ') }
    group = 'esy'
    version = gitVersion()
    repositories {
        mavenCentral()
    }
}

subprojects {
    plugins.withType(JavaPlugin).configureEach {
        compileJava.options.encoding = 'UTF-8'
        java.sourceCompatibility = JavaVersion.VERSION_21
        java.targetCompatibility = JavaVersion.VERSION_21
    }
    tasks.withType(Test).tap {
        configureEach {
            useJUnitPlatform {
                excludeEngines 'junit-vintage'
            }
            systemProperty "file.encoding", "UTF-8"
            testLogging {
                events "passed", "skipped", "failed"
                exceptionFormat "full"
                showExceptions true
                showCauses true
                showStackTraces true
                showStandardStreams false
            }
        }
    }
}

ext.VERSION = new File("${rootDir}/VERSION").text
tasks.register('versionTag', VersionTagTask.class) {
    group = 'versioning'
}

tasks.register('versionHistory', VersionHistoryTask.class) {
    group = 'documentation'
    changelog = layout.buildDirectory.file("reports/HISTORY.md")
    outputs.upToDateWhen { false }
}

tasks.register('versionRelease', VersionReleaseTask.class) {
    group = 'documentation'
    changelog = layout.buildDirectory.file("reports/RELEASE.md")
    outputs.upToDateWhen { false }
}

tasks.register('buildChangelog', BuildChangelogTask.class) {
    group = 'build'
    changelog = layout.buildDirectory.file("reports/CHANGELOG.md")
    outputs.upToDateWhen { false }
}

asciidoctor {
    sourceDir = file('doc/rest')
    outputDir = file('pages/html/server/rest')
    baseDir = file('.') // == project-dir
    attributes = [
        'project-dir': '.',
        'source-highlighter': 'coderay',
        'toc': 'left',
        'toclevels': 3,
        'sectanchors': true,
        'sectlinks': true,
        'linkattrs': true,
        'allow-uri-read': true        
    ]
}
