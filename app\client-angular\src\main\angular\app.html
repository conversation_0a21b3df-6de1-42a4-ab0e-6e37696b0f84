<div class="relative h-screen">
  <header class="z-1 fixed top-0 left-0 right-0 navbar bg-base-100 shadow-sm">
    <div class="navbar-start">
      <details
        class="dropdown"
        [open]="menuVisible()"
        (click)="onMenuToggle($event)"
      >
        <summary
          tabindex="0"
          class="btn btn-circle swap swap-rotate"
          [class.swap-active]="menuVisible()"
        >
          <svg
            class="swap-off h-5 w-5"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
          <svg
            class="swap-on h-5 w-5"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </summary>
        <ul
          tabindex="-1"
          class="menu dropdown-content bg-base-100 rounded-box z-1 mt-3 w-max p-2 shadow"
        >
          <li>
            <span class="capitalize">Client</span>
            <ul class="p-2">
              <li>
                <a
                  class="link"
                  (click)="onMenuClose($event)"
                  routerLink="/owner"
                  >Owner</a
                >
              </li>
              <li>
                <a class="link" (click)="onMenuClose($event)" routerLink="/pet"
                  >Pet</a
                >
              </li>
            </ul>
          </li>
          <li>
            <span class="capitalize">Clinic</span>
            <ul class="p-2">
              <li>
                <a
                  class="link"
                  (click)="onMenuClose($event)"
                  routerLink="/visit"
                  >Visit</a
                >
              </li>
              <li>
                <a class="link" (click)="onMenuClose($event)" routerLink="/vet"
                  >Vet</a
                >
              </li>
              <li>
                <a
                  class="link"
                  (click)="onMenuClose($event)"
                  routerLink="/enum/skill"
                  >Skill</a
                >
              </li>
              <li>
                <a
                  class="link"
                  (click)="onMenuClose($event)"
                  routerLink="/enum/species"
                  >Species</a
                >
              </li>
            </ul>
          </li>
        </ul>
      </details>
    </div>
    <div class="navbar-center">
      <a
        (click)="onMenuClose($event)"
        routerLink="/home"
        class="btn btn-ghost text-xl"
        >Petclinic</a
      >
    </div>
    <div class="navbar-end">
      <a class="link" (click)="onMenuClose($event)" routerLink="/help">?</a>
    </div>
  </header>

  <main class="z-2 py-16 h-full overflow-auto">
    <router-outlet />
  </main>

  <footer class="z-1 fixed bottom-0 left-0 right-0 dock">
    <a (click)="onMenuClose($event)" routerLink="/help">Impressum</a>
  </footer>
</div>
